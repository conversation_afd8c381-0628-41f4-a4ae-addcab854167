import { Injectable } from '@angular/core';
import { QueryEntity } from '@datorama/akita';
import { TeamProjectsState, TeamProjectsStore } from './team-projects.store';
import { Observable } from 'rxjs';
import { IPagination } from '@core/types';

@Injectable()
export class TeamProjectsQuery<EntityType> extends QueryEntity<TeamProjectsState<EntityType>> {

    constructor(protected store: TeamProjectsStore<EntityType>) {
        super(store);
    }

    public selectPagination(): Observable<IPagination> {
        return this.select((state) => {
            const { offset, limit, total } = state;

            return {
                currentPage: offset,
                totalCount: total,
                totalPages: Math.ceil(total / limit),
            };
        });
    }
}
