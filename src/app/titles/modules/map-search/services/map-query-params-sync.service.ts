import { Injectable } from '@angular/core';
import { MapStateWrapperService } from './map-state-wrapper.service';
import { Subject } from 'rxjs';
import { debounceTime, map, takeUntil } from 'rxjs/operators';
import { UrlParamsService } from '@services';

@Injectable()
export class MapQueryParamsSyncService {
    private readonly updateDebounceMs = 500;
    private destroy$ = new Subject<void>();

    constructor(
        private readonly mapStateService: MapStateWrapperService,
        private readonly urlParams: UrlParamsService,
    ) {
    }

    public initialize(): void {
        this.destroy$ = new Subject<void>();
        this.listenToMapCenterChange();
        this.listenToMapZoomChange();
    }

    public finalize(): void {
        this.destroy$.next();
        this.destroy$.complete();
    }

    private listenToMapCenterChange(): void {
        this.mapStateService.selectCenter
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(this.updateDebounceMs),
            )
            .subscribe((center) => {
                this.urlParams.addParams({ lat: center.lat, lng: center.lng });
            });
    }

    private listenToMapZoomChange(): void {
        this.mapStateService.selectZoom
            .pipe(
                takeUntil(this.destroy$),
                debounceTime(this.updateDebounceMs),
                map((zoom) => Math.round(zoom)),
            )
            .subscribe((zoom) => {
                this.urlParams.addParams({ zoom });
            });
    }
}
