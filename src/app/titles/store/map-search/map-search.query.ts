import { Query } from '@datorama/akita';
import { Injectable } from '@angular/core';
import { MapSearchState, MapSearchStore } from './map-search.store';
import { mergeWith, Observable } from 'rxjs';
import { filter, map } from 'rxjs/operators';
import { GeoJSONFeature, LngLat } from 'maplibre-gl';
import { ShortTitleInfo } from '../../types';
import { PreviousValueComparator } from '../../utils/previous-value-comparator.util';
import { MapStateWrapperService } from '../../modules/map-search/services/map-state-wrapper.service';

@Injectable()
export class MapSearchQuery extends Query<MapSearchState> {
    private readonly previousValueComparator = new PreviousValueComparator<
        Pick<MapSearchState, 'selectedFeatures' | 'permanentlyHighlightedTitleNumber'>
    >();

    constructor(
        protected readonly store: MapSearchStore,
        private readonly mapStateWrapperService: MapStateWrapperService,
    ) {
        super(store);
    }

    public getUniqueSelectedTitles(): Observable<ShortTitleInfo[]> {
        return this.select('sidePanelTitles')
            .pipe(
                map((selectedTitles) => {
                    const uniqueTitles: ShortTitleInfo[] = [];

                    selectedTitles.forEach((titleInfo) => {
                        const isFeatureAlreadyIncluded = uniqueTitles.some((el) => el.titleNumber === titleInfo.titleNumber);
                        if (!isFeatureAlreadyIncluded) {
                            uniqueTitles.push(titleInfo);
                        }
                    });

                    return uniqueTitles;
                }),
            );
    }

    public getMarkerPosition(): Observable<LngLat | null> {
        return this.select(['markerPosition', 'isMapComponentInitialized'])
            .pipe(
                map(() => this.getValue().markerPosition),
            );
    }

    public highlightingAreUpdated(): Observable<void> {
        return this.select(['temporaryHighlightedTitleNumber', 'permanentlyHighlightedTitleNumber'])
            .pipe(map(() => void 0));
    }

    public sidePanelContentIsUpdated(isDistinctUntilChangeEnabled: boolean | undefined = false): Observable<void> {
        return this.select(['permanentlyHighlightedTitleNumber', 'selectedFeatures'])
            .pipe(
                filter((value) =>
                    !isDistinctUntilChangeEnabled
                        ? true
                        : !this.previousValueComparator.compareAndSaveCurrentValue(value, (prev, curr) => {
                            const isHighlightedTitleNumberChanged = prev.permanentlyHighlightedTitleNumber !== curr.permanentlyHighlightedTitleNumber;
                            const isSelectedFeatureArraysLengthDiff = prev.selectedFeatures.length !== curr.selectedFeatures.length;

                            if (isHighlightedTitleNumberChanged || isSelectedFeatureArraysLengthDiff) {
                                return false;
                            }

                            const polyIdsOfPreviousArray = prev.selectedFeatures.map<string>((el) => el?.properties?.poly_id);
                            const polyIdsOfCurrentArray = curr.selectedFeatures.map<string>((el) => el?.properties?.poly_id);
                            const isSelectedFeaturesChanged = polyIdsOfPreviousArray.some((value, index) => value !== polyIdsOfCurrentArray[index]);

                            return !isSelectedFeaturesChanged;
                        }),
                ),
                map(() => void 0),
            );
    }

    public filtersAreUpdated(): Observable<void> {
        return this.select(['isLeaseholdsOn', 'isFreeholdsOn', 'featuresMap'])
            .pipe(
                mergeWith(this.mapStateWrapperService.selectZoom),
                map(() => void 0),
            );
    }

    public isSidebarVisible$(): Observable<boolean> {
        return this.select('isSidebarVisible')
            .pipe(
                map((isSidebarVisible) => isSidebarVisible),
            );
    }

    public isFiltersVisible$(): Observable<boolean> {
        return this.select('isFiltersVisible')
            .pipe(
                map((isFiltersVisible) => isFiltersVisible),
            );
    }

    public featuresUpdated(): Observable<void> {
        return this.select(['featuresMap'])
            .pipe(map(() => void 0));
    }

    public selectIsFeatureNotExist(): Observable<boolean> {
        return this.select('featuresMap')
            .pipe(
                map((features) => !features || !Object.keys(features).length),
            );
    }

    public focusedFeaturesUpdated(): Observable<GeoJSONFeature[]> {
        return this.select('focusedFeatures')
            .pipe(
                filter((el) => !!el),
            );
    }

    public isTitleNumberPermanentlyHighlighted(titleNumber: string): boolean {
        return this.getValue().permanentlyHighlightedTitleNumber === titleNumber;
    }

    public isSelectedMultipleFeatures(): Observable<boolean> {
        return this.select('selectedFeatures')
            .pipe(
                map((selectedTitles) => {
                    const uniqueTitleNumbers = {};

                    selectedTitles.forEach((titleInfo) => {
                        const titleNumber = titleInfo.properties.title_number;
                        uniqueTitleNumbers[titleNumber] = titleInfo;
                    });

                    return Object.keys(uniqueTitleNumbers).length > 1;
                }),
            );
    }
}
