import { Injectable } from '@angular/core';
import { EntityState, EntityStore, StoreConfig } from '@datorama/akita';
import { PaginatedData } from '@core/types';

export type TeamProjectsState<EntityType> = EntityState<EntityType>
    & Omit<PaginatedData<EntityType>, 'items'>;

const defaultState: Omit<PaginatedData<unknown>, 'items'> = {
    offset: 0,
    limit: 0,
    total: 0,
};

@Injectable()
@StoreConfig({ name: 'team-projects', resettable: true })
export class TeamProjectsStore<EntityType> extends EntityStore<TeamProjectsState<EntityType>> {

    constructor() {
        super(defaultState);
    }

}
