import { Inject, Injectable } from '@angular/core';
import { HttpClient, HttpParams, HttpResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { PaginatedData, ProjectMetrics } from '@core/types';
import { TeamProjectsQueryParams } from '../types/teamwork-query-params.type';

@Injectable()
export class TeamworkApi<EntityType> {

    constructor(
        @Inject('appName')
        private readonly appName: string,
        private readonly http: HttpClient,
    ) {
    }

    public getProjects(queryParams?: TeamProjectsQueryParams): Observable<HttpResponse<PaginatedData<EntityType>>> {
        const url = `/api/${this.appName}/teamwork`;
        let params = new HttpParams();

        if (queryParams?.q) {
            params = params.set('q', queryParams.q);
        }
        if (queryParams?.limit !== undefined) {
            params = params.set('limit', queryParams.limit.toString());
        }
        if (queryParams?.offset !== undefined) {
            params = params.set('start', queryParams.offset.toString());
        }
        if (queryParams?.sort) {
            params = params.set('sort', queryParams.sort);
        }
        if (queryParams?.order) {
            const order = queryParams.order === 'asc' ? 'asc' : 'dsc';
            params = params.set('order', order);
        }

        return this.http.get<PaginatedData<EntityType>>(url, { params, observe: 'response' });
    }

    public getMetrics(): Observable<ProjectMetrics> {
        return this.http.get<ProjectMetrics>(`/api/${this.appName}/teamwork/active`);
    }
}
