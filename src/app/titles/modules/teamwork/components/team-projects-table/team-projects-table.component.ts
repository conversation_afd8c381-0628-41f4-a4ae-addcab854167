import { AfterViewInit, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { TitleProject } from '../../types/title-project.type';
import { MatTableDataSource } from '@angular/material/table';
import { MatSort } from '@angular/material/sort';
import { MatPaginator } from '@angular/material/paginator';

import { takeUntil, tap } from 'rxjs/operators';
import { merge, Subject } from 'rxjs';

import { TableRowsAmountService } from '@services';
import { ICollectionHeightOffset, ICollectionPageEvent, IPagination } from '@core/types';
import { animate, state, style, transition, trigger } from '@angular/animations';

const tableHeightOffset: ICollectionHeightOffset = {
    header: 64,
    tableHeader: 54,
    tableHeaderColumns: 44,
    paginator: 56,
    footerButton: 0,
};
const rowHeightPx = 59;


@Component({
    selector: 'avl-team-projects-table',
    templateUrl: './team-projects-table.component.html',
    styleUrls: ['./team-projects-table.component.scss'],
    animations: [
        trigger('detailExpand', [
            state('collapsed', style({ height: '0', minHeight: '0', padding: 0 })),
            state('expanded', style({ height: '*', display: '*' })),
            transition('expanded <=> collapsed', animate('225ms cubic-bezier(0.4, 0.0, 0.2, 1)')),
        ]),
    ],
})
export class TeamProjectsTableComponent implements OnInit, OnChanges, AfterViewInit, OnDestroy {
    @Input()
    public isLoading = true;

    @Input()
    public projects: TitleProject[] = [];

    @Input()
    public pagination: IPagination = { totalCount: 0 };

    @Output()
    public pageChanged = new EventEmitter<ICollectionPageEvent>();

    @Output()
    public projectOpened = new EventEmitter<TitleProject>();

    @Output()
    public reportDownloaded = new EventEmitter<string>();

    @ViewChild(MatSort, { static: true })
    public sort: MatSort;

    @ViewChild(MatPaginator, { static: true })
    public paginator: MatPaginator;

    public displayedColumns: string[] = ['projectName', 'owner', 'lastOpenedAt', 'titles', 'showMore', 'actions'];
    public tableSource = new MatTableDataSource<TitleProject>([]);
    public tableRowsAmount: number;

    private readonly destroy$ = new Subject<void>();

    private expandedRowId: string | null = null;

    constructor(
        private readonly tableRowsAmountService: TableRowsAmountService,
    ) {
    }

    public ngOnInit(): void {
        this.calculateRowsAmount();
        this.resetToFirstPage();
    }

    public ngOnChanges(): void {
        this.tableSource.data = this.projects;
    }

    public ngAfterViewInit(): void {
        this.sort.sortChange.subscribe(() => this.paginator.pageIndex = 0);
        this.setupPageChangeListener();
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.unsubscribe();
    }

    public openProject(item: TitleProject): void {
        this.projectOpened.emit(item);
    }

    public downloadReport(item: TitleProject): void {
        this.reportDownloaded.emit(item.officialCopiesUri);
    }

    public resetToFirstPage(): void {
        this.paginator.pageIndex = 0;
        this.paginator.firstPage();
        this.pageChanged.emit({
            pageIndex: 0,
            pageSize: this.tableRowsAmount,
            sort: this.sort.active,
            order: this.sort.direction,
        });
    }

    public rowTrack(index: number, item: TitleProject): string {
        return item.id;
    }

    public expandDetailsRow(item: TitleProject): void {
        const rowId = item.id;
        this.expandedRowId = rowId === this.expandedRowId ? null : rowId;
    }

    public isDetailsExpanded(item: TitleProject): boolean {
        return item.id === this.expandedRowId;
    }

    private calculateRowsAmount(): void {
        this.tableRowsAmount = this.tableRowsAmountService
            .calculateRowsAmount(tableHeightOffset, rowHeightPx);
    }

    private setupPageChangeListener(): void {
        merge(this.sort.sortChange, this.paginator.page)
            .pipe(
                takeUntil(this.destroy$),
                tap(() => {
                    this.pageChanged.emit({
                        pageIndex: this.paginator.pageIndex,
                        pageSize: this.paginator.pageSize,
                        sort: this.sort.active,
                        order: this.sort.direction,
                    });
                }))
            .subscribe();
    }
}
