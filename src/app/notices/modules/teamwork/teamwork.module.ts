import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TeamProjectsPageComponent } from './components/team-projects-page/team-projects-page.component';
import { TeamProjectsTableComponent } from './components/team-projects-table/team-projects-table.component';
import { SharedModule } from '@shared/shared.module';
import { MatMenuModule } from '@angular/material/menu';
import { TeamworkCommonModule } from '../../../teamwork-common/teamwork-common.module';


@NgModule({
    declarations: [
        TeamProjectsPageComponent,
        TeamProjectsTableComponent,
    ],
    imports: [
        CommonModule,
        SharedModule,
        MatMenuModule,
        TeamworkCommonModule,
    ],
})
export class TeamworkModule {
}
