import { ChangeDetectionStrategy, Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { mergeWith, Observable, Subject, tap } from 'rxjs';
import { FeatureSelectionEvent } from '../../../modules/map-search/components/map/map.component';
import { LngLat, Map } from 'maplibre-gl';
import { MapSearchQuery, MapSearchService } from '../../../store';
import { MapDefaultSourceService } from '../../../modules/map-search/services/map-default-source.service';
import { map, takeUntil } from 'rxjs/operators';
import { MapNavigationService } from '../../../modules/map-search/services/map-navigation.service';
import { SelectionToolContextService } from '../../../modules/sam/services/selection-tool-context.service';
import { SelectTool } from '../../../modules/sam/enums/select-tool.enum';
import { BoundsEndSelectionEventData } from '../../../modules/sam/types/bounds-end-selection-event-data.type';
import { MapPopupService } from '../../../modules/map-search/services/map-popup.service';
import { MapRequestsTransformationService } from '../../../modules/map-search/services/map-requests-transformation.service';
import { SamStateQuery } from '../../../modules/sam/stores/sam-state/sam-state-query.service';
import { MapSnackbarMessage } from '../../../modules/map-search/types/map-snackbar-message.type';
import { CircleEndSelectionEventData } from '../../../modules/sam/types/circle-end-selection-event-data.type';
import { PolygonEndSelectionEventData } from '../../../modules/sam/types/polygon-end-selection-event-data.type';
import { samMinZoomVisibility } from '../../../constants/land-registry-search.constants';
import { SamStateService } from '../../../modules/sam/stores/sam-state/sam-state.service';
import { samHints } from '../../../modules/sam/constants/sam-hints.constants';
import { MapSnackbarStatus } from '../../../modules/map-search/enums/map-snackbar-status.enum';
import { SamHint } from '../../../modules/sam/enums/sam-hint.enum';
import { NoPolygonNotificationsService } from '../../../modules/map-search/services/no-polygon-notifications.service';
import { ProjectDetailsQuery } from '../../../../project-details/stores/project-details/project-details.query';
import { MapStateWrapperService } from '../../../modules/map-search/services/map-state-wrapper.service';
import { MapQueryParamsSyncService } from '../../../modules/map-search/services/map-query-params-sync.service';
import { MapFiltersSyncService } from '../../../modules/map-search/services/map-filters-sync.service';

@Component({
    selector: 'avl-land-registry-map-search',
    templateUrl: './land-registry-map-search.component.html',
    styleUrls: ['./land-registry-map-search.component.scss'],
    animations: [
        trigger('slideInOut', [
            state('in', style({
                transform: 'translate3d(0,0,0)',
            })),
            state('out', style({
                transform: 'translate3d(100%, 0, 0)',
            })),
            transition('in => out', animate('400ms ease-in-out')),
            transition('out => in', animate('400ms ease-in-out')),
        ]),
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LandRegistryMapSearchComponent implements OnInit, OnDestroy {
    public isSidebarVisible$: Observable<boolean>;
    public center: LngLat;
    public zoom: number;
    public isMapLoading$: Observable<boolean>;
    public isSamUnavailable$: Observable<boolean>;
    public isSelectionUnderPinBlocked$: Observable<boolean>;
    public isMapInteractionAvailable$: Observable<boolean>;
    public samMessages$: Observable<MapSnackbarMessage[]>;
    public mapInstance: Map | null = null;

    private readonly destroy$ = new Subject<void>();

    constructor(
        private readonly mapSearchQuery: MapSearchQuery,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly mapSearchService: MapSearchService,
        private readonly mapDefaultSourceService: MapDefaultSourceService,
        private readonly mapNavigationService: MapNavigationService,
        private readonly selectionToolService: SelectionToolContextService,
        private readonly mapPopupService: MapPopupService,
        private readonly mapRequestsTransformationService: MapRequestsTransformationService,
        private readonly samStateQuery: SamStateQuery,
        private readonly samStateService: SamStateService,
        private readonly noPolygonNotificationsService: NoPolygonNotificationsService,
        private readonly mapStateService: MapStateWrapperService,
        private readonly mapQueryParamsSyncService: MapQueryParamsSyncService,
        private readonly mapSidePanelSyncService: MapFiltersSyncService,
    ) {
    }

    public ngOnInit(): void {
        this.center = this.mapStateService.center;
        this.zoom = this.mapStateService.zoom;
        this.isSamUnavailable$ = this.mapStateService.selectZoom
            .pipe(
                mergeWith(this.samStateQuery.select('isEnabled')),
                map(() => {
                    const zoom = this.mapStateService.zoom;
                    const isSamEnabled = this.samStateQuery.getValue().isEnabled;

                    return !isSamEnabled && zoom < samMinZoomVisibility;
                }),
            );
        this.mapDefaultSourceService.featuresLoadedEvent
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => this.noPolygonNotificationsService.handleNoPolygonsMessage());
        this.samMessages$ = this.samStateQuery.selectMessages();
        this.isSidebarVisible$ = this.mapSearchQuery.isSidebarVisible$();
        this.mapRequestsTransformationService.initialize(this.projectDetailsQuery.projectId);
        this.isMapLoading$ = this.mapSearchQuery.selectLoading()
            .pipe(
                tap((isLoading) => this.updateSamLoadingHint(isLoading)),
            );
        this.isSelectionUnderPinBlocked$ = this.mapSearchQuery.select('isSelectionUnderPinBlocked');
        this.isMapInteractionAvailable$ = this.samStateQuery.selectIsMapInteractionAvailable();
        this.mapSearchService.initUrlParams();
        this.selectionToolService.confirmSelectionEvent()
            .subscribe((eventData) => {
                switch (eventData.type) {
                    case SelectTool.bounds:
                        const bounds = eventData as BoundsEndSelectionEventData;
                        this.mapSearchService.fetchMapGeoJson(bounds);
                        break;
                    case SelectTool.circle:
                        const circleData = eventData as CircleEndSelectionEventData;
                        this.mapSearchService.fetchGeoJsonForCircle(circleData.center, circleData.radius);
                        break;
                    case SelectTool.polygon:
                        const polygonData = eventData as PolygonEndSelectionEventData;
                        this.mapSearchService.fetchGeoJsonForPolygon(polygonData.points);
                        break;
                }
            });
        this.mapStateService.selectIsZoomAboveMin
            .pipe(takeUntil(this.destroy$))
            .subscribe((isZoomApplicable) => {
                if (isZoomApplicable) {
                    this.mapPopupService.unblock();
                } else {
                    this.mapPopupService.block();
                }
            });
    }

    public ngOnDestroy(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.selectionToolService.deactivate();
        this.mapSearchService.deactivateSelectToolsMode();
        this.mapQueryParamsSyncService.finalize();
        this.mapSidePanelSyncService.finalize();
        this.noPolygonNotificationsService.finalize();
    }

    public onMapInit(map: Map): void {
        this.mapInstance = map;
        this.mapSearchService.setMapComponentInitialized();
        this.subscribeOnMapChanges();
        this.noPolygonNotificationsService.initialize(map);
        this.mapQueryParamsSyncService.initialize();
        this.mapSidePanelSyncService.initialize();
    }

    public onMapClick({ features, point }: FeatureSelectionEvent): void {
        const isOverPolygon = !!features.length;

        this.mapSearchService.setMarkerPosition(point);
        this.mapSearchService.resetHighlighting();

        if (isOverPolygon) {
            const titleNumber = this.mapSearchService.whichTitleNumberHighlight(features);
            this.mapSearchService.highlightFeaturePermanently(titleNumber);
        }

        this.noPolygonNotificationsService.handleNoPolygonsMessage(isOverPolygon);
    }

    public onFeaturesSelected({ features }: FeatureSelectionEvent): void {
        if (!this.samStateQuery.isMapInteractionAvailable()) {
            return;
        }

        const searchedTitleNumber = this.mapSearchQuery.getValue().searchedTitleNumber;
        const titleNumber = searchedTitleNumber || this.mapSearchService.whichTitleNumberHighlight(features);

        if (titleNumber) {
            this.mapSearchService.fetchSelectedTitles(titleNumber);
            this.mapSearchService.setIsSidePanelVisible(true);
        }

        if (features.length) {
            const isHighlightedTitleExists = !!this.mapSearchQuery.getValue().permanentlyHighlightedTitleNumber;
            if (!isHighlightedTitleExists) {
                this.mapSearchService.highlightFeaturePermanently(titleNumber);
            }
        } else {
            this.mapSearchService.closeSidePanelAndResetHighlighting();
        }
    }

    private subscribeOnMapChanges(): void {
        this.mapSearchQuery.getMarkerPosition()
            .pipe(takeUntil(this.destroy$))
            .subscribe((point) => this.mapNavigationService.pointOn(point || null));
        this.mapSearchQuery.featuresUpdated()
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
                const loadedFeatures = this.mapSearchQuery.getValue().featuresMap ?? {};
                const selectedFeatures = this.mapSearchQuery.getValue().selectedFeatures;
                this.mapDefaultSourceService.addFeatures(loadedFeatures, selectedFeatures);
            });
        this.mapSearchQuery.focusedFeaturesUpdated()
            .pipe(takeUntil(this.destroy$))
            .subscribe((features) => this.mapNavigationService.jumpToFeature(features));
        this.mapSearchQuery.highlightingAreUpdated()
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
                const temporaryHighlighted = this.mapSearchQuery.getValue().temporaryHighlightedTitleNumber;
                const permanentlyHighlighted = this.mapSearchQuery.getValue().permanentlyHighlightedTitleNumber;
                const isTitleExists = !!temporaryHighlighted || !!permanentlyHighlighted;

                this.mapDefaultSourceService.highlightFeature(temporaryHighlighted || permanentlyHighlighted);
                this.mapDefaultSourceService.updateSelectedFeaturesList(isTitleExists
                    ? [temporaryHighlighted || permanentlyHighlighted]
                    : []);
            });
        this.mapSearchQuery.filtersAreUpdated()
            .pipe(takeUntil(this.destroy$))
            .subscribe(() => {
                const isFreeholdsOn = this.mapSearchQuery.getValue().isFreeholdsOn;
                const isLeaseholdsOn = this.mapSearchQuery.getValue().isLeaseholdsOn;
                const isZoomAcceptable = this.samStateQuery.getValue().isEnabled || this.mapStateService.isZoomAboveMin;
                this.mapDefaultSourceService.updateFeaturesFilter(isFreeholdsOn, isLeaseholdsOn, isZoomAcceptable);
            });
    }

    private updateSamLoadingHint(isLoading: boolean): void {
        const hintId = SamHint.loadingData;

        if (isLoading) {
            const message = {
                message: samHints[hintId],
                status: MapSnackbarStatus.hint,
            };
            this.samStateService.updateMessages(message, hintId);
        } else {
            this.samStateService.clearMessages(hintId);
        }
    }
}
