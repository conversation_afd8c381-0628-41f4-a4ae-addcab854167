import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TeamworkApi } from './api/teamwork.api';
import { TeamProjectsService } from './stores/team-projects.service';
import { TeamProjectsQuery } from './stores/team-projects.query';
import { TeamProjectsStore } from './stores/team-projects.store';


@NgModule({
    declarations: [],
    imports: [
        CommonModule,
    ],
    providers: [
        TeamworkApi,
        TeamProjectsService,
        TeamProjectsStore,
        TeamProjectsQuery,
    ],
})
export class TeamworkCommonModule {
}
