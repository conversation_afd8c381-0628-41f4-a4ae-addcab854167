import { Injectable } from '@angular/core';
import { TeamworkApi } from '../api/teamwork.api';
import { TeamProjectsQueryParams } from '../types/teamwork-query-params.type';
import { LoggerService } from '@services';
import { TeamProjectsStore } from './team-projects.store';
import { finalize, switchMap, takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';

@Injectable()
export class TeamProjectsService<EntityType> {
    private loadProjectsSubject: Subject<TeamProjectsQueryParams | undefined>;
    private destroy$: Subject<void>;

    constructor(
        private readonly api: TeamworkApi<EntityType>,
        private readonly store: TeamProjectsStore<EntityType>,
        private readonly log: LoggerService,
    ) {
    }

    public init(): void {
        this.loadProjectsSubject = new Subject<TeamProjectsQueryParams | undefined>();
        this.destroy$ = new Subject<void>();
        this.setupLoadProjectsStream();
    }

    public finalize(): void {
        this.destroy$.next();
        this.destroy$.complete();
        this.loadProjectsSubject.complete();
    }

    public loadProjects(queryParams?: TeamProjectsQueryParams): void {
        this.loadProjectsSubject.next(queryParams);
    }

    public clear(): void {
        this.store.reset();
    }

    private setupLoadProjectsStream(): void {
        this.loadProjectsSubject
            .pipe(
                takeUntil(this.destroy$),
                switchMap((queryParams) => {
                    this.store.setLoading(true);

                    return this.api.getProjects(queryParams)
                        .pipe(
                            finalize(() => this.store.setLoading(false)),
                        );
                }),
            )
            .subscribe({
                next: (response) => {
                    const items = response.body.items;
                    const paginatedData = {
                        offset: response.body.offset,
                        limit: response.body.limit,
                        total: response.body.total,
                    };

                    this.store.set(items);
                    this.store.update(paginatedData);
                },
                error: (error) => {
                    this.log.error('Team projects loading error', error);

                    if (error.status === 404) {
                        this.store.set([]);
                        return;
                    }

                    this.store.setError('Failed to load team projects');
                },
            });
    }
}
