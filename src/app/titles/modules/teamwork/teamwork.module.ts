import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TeamProjectsPageComponent } from './components/team-projects-page/team-projects-page.component';
import { TeamProjectDetailsComponent } from './components/team-project-details/team-project-details.component';
import { TeamProjectsTableComponent } from './components/team-projects-table/team-projects-table.component';
import { TeamProjectSchedulesComponent } from './components/team-project-schedules/team-project-schedules.component';
import { TeamProjectPurchasedTitlesComponent } from './components/team-project-purchased-titles/team-project-purchased-titles.component';
import { SharedModule } from '@shared/shared.module';
import { MatMenuModule } from '@angular/material/menu';
import { TeamworkCommonModule } from '../../../teamwork-common/teamwork-common.module';

@NgModule({
    declarations: [
        TeamProjectsPageComponent,
        TeamProjectDetailsComponent,
        TeamProjectsTableComponent,
        TeamProjectSchedulesComponent,
        TeamProjectPurchasedTitlesComponent,
    ],
    imports: [
        CommonModule,
        SharedModule,
        MatMenuModule,
        TeamworkCommonModule,
    ],
})
export class TeamworkModule {
}
