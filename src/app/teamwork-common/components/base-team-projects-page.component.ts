import { Observable } from 'rxjs';
import { ICollectionPageEvent, IPagination } from '@core/types';
import { TeamProjectsService } from '../stores/team-projects.service';
import { TeamProjectsQuery } from '../stores/team-projects.query';
import { ProjectSort, TeamProjectsQueryParams } from '../types/teamwork-query-params.type';
import { OnDestroy, OnInit } from '@angular/core';

export abstract class BaseTeamProjectsPageComponent<EntityType> implements OnInit, OnDestroy {
    public isLoading$ = this.teamProjectsQuery.selectLoading();
    public projects$ = this.teamProjectsQuery.selectAll();
    public pagination$: Observable<IPagination> = this.teamProjectsQuery.selectPagination();

    private lastSearchQuery: string | null = null;

    constructor(
        private readonly teamProjectsService: TeamProjectsService<EntityType>,
        private readonly teamProjectsQuery: TeamProjectsQuery<EntityType>,
    ) {
    }

    public ngOnInit(): void {
        this.teamProjectsService.init();
    }

    public ngOnDestroy(): void {
        this.teamProjectsService.finalize();
        this.teamProjectsService.clear();
    }

    public onSearch(query: string): void {
        this.lastSearchQuery = query.length ? query.trim() : null;
        this.resetPaginationToFirstPage();
    }

    public onPageChanged(page: ICollectionPageEvent): void {
        this.updateProjectsList(page);
    }

    protected abstract resetPaginationToFirstPage(): void;

    protected abstract normalizeSortOption(sort: string | undefined): string | null;

    private updateProjectsList(page: ICollectionPageEvent): void {
        const sortOptions = ['lastOpenedAt', 'projectName', 'matterNumber', 'owner', 'documentCount'];
        const normalizedSortOptions = this.normalizeSortOption(page.sort);
        const sort = sortOptions.includes(normalizedSortOptions)
            ? normalizedSortOptions as ProjectSort
            : undefined;
        const params: TeamProjectsQueryParams = {
            limit: page.pageSize,
            offset: page.pageIndex * page.pageSize,
            sort: sort,
            order: page.order,
            q: this.lastSearchQuery,
        };

        this.teamProjectsService.loadProjects(params);
    }
}
