import { Injectable } from '@angular/core';
import { MapElementsBase } from './map-elements-base';
import { BehaviorSubject, Observable } from 'rxjs';
import { LngLat, Map } from 'maplibre-gl';
import { defaultMapCenter, defaultMapZoom } from '../constants/defaults.constants';
import { ProfileService } from '@services';
import { map } from 'rxjs/operators';
import { mapSearchMaxZoom, mapSearchMinZoom } from '../../../constants/land-registry-search.constants';

@Injectable()
export class MapStateWrapperService extends MapElementsBase {
    private readonly moveEvent$ = new BehaviorSubject<LngLat>(defaultMapCenter);
    private readonly zoomEvent$ = new BehaviorSubject<number>(defaultMapZoom);

    constructor(
        private readonly profile: ProfileService,
    ) {
        super();

        this.onMove = this.onMove.bind(this);
        this.onZoom = this.onZoom.bind(this);
    }

    public get center(): LngLat {
        return this.moveEvent$.getValue();
    }

    public get zoom(): number {
        return this.zoomEvent$.getValue();
    }

    public get isZoomAboveMin(): boolean {
        return this.zoomEvent$.getValue() >= this.profile.mapSearchMinZoomFeatureVisibility$.getValue();
    }

    public get selectCenter(): Observable<LngLat> {
        return this.moveEvent$.asObservable();
    }

    public get selectZoom(): Observable<number> {
        return this.zoomEvent$.asObservable();
    }

    public get selectIsZoomAboveMin(): Observable<boolean> {
        return this.zoomEvent$.asObservable()
            .pipe(
                map((zoom) => zoom >= this.profile.mapSearchMinZoomFeatureVisibility$.getValue()),
            );
    }

    public get selectIsMaxZoomAchieved(): Observable<boolean> {
        return this.zoomEvent$.asObservable()
            .pipe(
                map((zoom) => zoom >= mapSearchMaxZoom),
            );
    }

    public get selectIsMinZoomAchieved(): Observable<boolean> {
        return this.zoomEvent$.asObservable()
            .pipe(
                map((zoom) => zoom <= mapSearchMinZoom),
            );
    }

    public setCenter(center: LngLat): void {
        if (this.isMapInitialized()) {
            this.map.setCenter(center);
        } else {
            this.moveEvent$.next(center);
        }
    }

    public setZoom(zoom: number): void {
        if (this.isMapInitialized()) {
            this.map.setZoom(zoom);
        } else {
            this.zoomEvent$.next(zoom);
        }
    }

    public applyMinZoom(): void {
        const minZoom = this.profile.mapSearchMinZoomFeatureVisibility$.getValue();
        this.map.setZoom(minZoom);
    }

    public initialize(map: Map): void {
        super.initialize(map);
        this.subscribeOnMapEvents();
    }

    public finalize(): void {
        this.unsubscribeFromMapEvents();
        super.finalize();
    }

    private subscribeOnMapEvents(): void {
        this.map.on('move', this.onMove);
        this.map.on('zoom', this.onZoom);
    }

    private unsubscribeFromMapEvents(): void {
        this.map.off('move', this.onMove);
        this.map.off('zoom', this.onZoom);
    }

    private onMove(): void {
        const center = this.map.getCenter();
        this.moveEvent$.next(center);
    }

    private onZoom(): void {
        const zoom = this.map.getZoom();
        this.zoomEvent$.next(zoom);
    }
}
