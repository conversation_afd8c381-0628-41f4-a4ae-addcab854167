<div class="collection__table-container team-projects-table-container">
    <div class="collection__table-wrap">
        <table
            class="collection__table"
            mat-table
            matSort
            matSortDisableClear
            multiTemplateDataRows
            [trackBy]="rowTrack"
            [dataSource]="tableSource"
        >
            <!-- Name Column -->
            <ng-container matColumnDef="projectName">
                <th
                    mat-header-cell
                    mat-sort-header
                    *matHeaderCellDef
                >
                    Project Name
                </th>
                <td
                    mat-cell
                    *matCellDef="let row"
                >
                    <div class="project-info-column">
                        <span class="project-name">{{ row.projectName || '-' }}</span>
                        <span class="matter-number">{{ row.matterNumber || '-' }}</span>
                    </div>
                </td>
            </ng-container>

            <!-- Owner's Email Column -->
            <ng-container matColumnDef="owner">
                <th
                    mat-header-cell
                    mat-sort-header
                    *matHeaderCellDef
                >
                    Owner
                </th>
                <td
                    mat-cell
                    *matCellDef="let row"
                >
                    {{ row.owner || '-' }}
                </td>
            </ng-container>

            <!-- Last Updated Column -->
            <ng-container matColumnDef="lastOpenedAt">
                <th
                    mat-header-cell
                    mat-sort-header
                    *matHeaderCellDef
                >
                    Last Updated
                </th>
                <td
                    mat-cell
                    *matCellDef="let row"
                >
                    {{ (row.lastOpenedAt | date:'d MMMM y') || '-' }}
                </td>
            </ng-container>

            <!-- Titles Column -->
            <ng-container matColumnDef="titles">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                >
                    Titles
                </th>
                <td
                    mat-cell
                    *matCellDef="let row"
                >
                    {{ (row.titleNumbers ?? []).length ?? 0 }}
                </td>
            </ng-container>

            <!-- Show More Column -->
            <ng-container matColumnDef="showMore">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                ></th>
                <td
                    mat-cell
                    *matCellDef="let row"
                >
                    <button
                        class="clear-btn-styles show-more-btn"
                        [class.show-more-btn--active]="isDetailsExpanded(row)"
                        (click)="expandDetailsRow(row)"
                    >
                        <span>View</span>
                        <mat-icon
                            class="show-more-btn__icon"
                            [class.show-more-btn__icon--spin-up]="isDetailsExpanded(row)"
                            svgIcon="extend-button"
                        ></mat-icon>
                    </button>
                </td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
                <th
                    mat-header-cell
                    *matHeaderCellDef
                ></th>
                <td
                    mat-cell
                    *matCellDef="let row"
                >
                    <button
                        class="clear-btn-styles actions-btn"
                        [matMenuTriggerFor]="defaultProfileMenu"
                    >
                        <mat-icon
                            class="actions-btn__icon"
                            svgIcon="menu-dots"
                        ></mat-icon>
                    </button>
                    <mat-menu
                        #defaultProfileMenu="matMenu"
                        class="teamwork-page-actions-menu"
                        xPosition="before"
                    >
                        <button
                            mat-menu-item
                            class="teamwork-page-actions-menu__button"
                            [disabled]="!row.officialCopiesUri"
                            (click)="downloadReport(row)"
                        >
                            <mat-icon
                                svgIcon="download-type-2"
                            ></mat-icon>
                            <span>Download Official Copies</span>
                        </button>
                        <button
                            mat-menu-item
                            (click)="openProject(row)"
                        >
                            <mat-icon
                                svgIcon="file-preview"
                            ></mat-icon>
                            <span>Open Project</span>
                        </button>
                    </mat-menu>
                </td>
            </ng-container>

            <!-- Details Row -->
            <ng-container matColumnDef="expandedDetail">
                <td
                    mat-cell
                    *matCellDef="let row"
                    [attr.colspan]="displayedColumns.length"
                >
                    <div
                        *ngIf="!isLoading"
                        class="expanded-details-row"
                        [@detailExpand]="isDetailsExpanded(row) ? 'expanded' : 'collapsed'"
                    >
                        <avl-team-project-details
                            [project]="row"
                        ></avl-team-project-details>
                    </div>
                </td>
            </ng-container>

            <tr
                mat-header-row
                *matHeaderRowDef="displayedColumns"
            ></tr>
            <tr
                mat-row
                [hidden]="isLoading"
                *matRowDef="let row; columns: displayedColumns;"
            ></tr>
            <tr
                mat-row
                *matRowDef="let row; columns: ['expandedDetail']"
                class="details-expandable-row"
            ></tr>
        </table>
        <avl-table-loading-placeholder
            *ngIf="isLoading"
            [columns]="displayedColumns"
            [size]="tableRowsAmount"
        ></avl-table-loading-placeholder>
    </div>
    <avl-table-no-data-disclaimer
        *ngIf="!isLoading && !projects.length"
        iconName="file-chart-grey"
        message="No team projects"
    ></avl-table-no-data-disclaimer>
    <mat-paginator
        [length]="pagination.totalCount"
        [class.hidden]="!projects.length"
        [pageSize]="tableRowsAmount"
        [hidePageSize]="true"
    ></mat-paginator>
</div>
