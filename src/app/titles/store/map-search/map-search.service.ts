import { Injectable } from '@angular/core';
import { MapSearchStore } from './map-search.store';
import { CacheService, LoggerService, ProfileService, UrlParamsService } from '@services';
import { catchError, finalize, shareReplay } from 'rxjs/operators';
import { ITitleInfo, ShortTitleInfo } from '../../types';
import { GeoJSONFeature, LngLat } from 'maplibre-gl';
import { MapUtilsService } from '../../services/map-utils.service';
import { MapFeaturesService } from '../../modules/map-search/services/map-features.service';
import { Observable, Subscription, throwError } from 'rxjs';
import { LandRegistrySearchSource } from '../../enums/land-registry-search-source.enum';
// Don't simplify: it causes circular dependency issue
import { HmLandRegistryService } from '../../services/land-registry';
import { MapBounds } from '../../modules/map-search/types/mapping-bounds.type';
import { MappingApi } from '../../modules/map-search/api/mapping.api';
import { MapPopupService } from '../../modules/map-search/services/map-popup.service';
import { SamStateService } from '../../modules/sam/stores/sam-state/sam-state.service';
import { MarkerService } from '../../modules/map-search/services/marker.service';
import { SamStateQuery } from '../../modules/sam/stores/sam-state/sam-state-query.service';
import { MapSearchQuery } from './map-search.query';
import { ProjectScopedStorageService } from '../../../core/services/project-scoped-storage.service';
import { GoogleMapsUtilsService } from '../../modules/map-search/services/google-maps-utils.service';
import { MarkerMenuAction } from '../../modules/map-search/enums/marker-menu-action.enum';
import { ProjectDetailsQuery } from '../../../project-details/stores/project-details/project-details.query';
import { MapStateWrapperService } from '../../modules/map-search/services/map-state-wrapper.service';
import { MapNavigationService } from '../../modules/map-search/services/map-navigation.service';

@Injectable()
export class MapSearchService {
    private readonly urlMapParams = ['zoom', 'lat', 'lng', 'titleNumber', 'markerLat', 'markerLng', 'isFreeholdsOn', 'isLeaseholdsOn', 'searchType'];
    private highlightDebounceTimer: NodeJS.Timeout;
    private isSelectToolsDisplayingPolygonsModeSub?: Subscription;

    constructor(
        private readonly store: MapSearchStore,
        private readonly query: MapSearchQuery,
        private readonly urlParams: UrlParamsService,
        private readonly projectDetailsQuery: ProjectDetailsQuery,
        private readonly mappingApi: MappingApi,
        private readonly mapUtilsService: MapUtilsService,
        private readonly mapFeaturesService: MapFeaturesService,
        private readonly hmLandRegistryService: HmLandRegistryService,
        private readonly titleDetailsCacheService: CacheService<ITitleInfo[]>,
        private readonly log: LoggerService,
        private readonly mapPopupService: MapPopupService,
        private readonly markerService: MarkerService,
        private readonly samStateService: SamStateService,
        private readonly samStateQuery: SamStateQuery,
        private readonly projectScopedStorage: ProjectScopedStorageService,
        private readonly googleMaps: GoogleMapsUtilsService,
        private readonly profile: ProfileService,
        private readonly mapStateWrapperService: MapStateWrapperService,
        private readonly mapNavigationService: MapNavigationService,
    ) {
        this.projectScopedStorage.registerStore({
            storeName: 'map-search',
            store: this.store,
            query: this.query,
            select: (state) => ({
                isFreeholdsOn: state.isFreeholdsOn,
                isLeaseholdsOn: state.isLeaseholdsOn,
            }),
        });
        this.subscribeOnMarkerContextMenuEvents();
    }

    public pointOnSelectedTitleOnGoogleMaps(): void {
        const storeValues = this.store.getValue();
        const zoom = this.mapStateWrapperService.zoom;
        const selectedTitleNumber = storeValues.details?.titleNumber;
        const mapFeatures = storeValues.featuresMap ?? {};
        const selectedFeatures = !!selectedTitleNumber && mapFeatures[selectedTitleNumber];

        if (selectedFeatures) {
            const geoJSONFeatures = Array.isArray(selectedFeatures) ? selectedFeatures : [selectedFeatures];
            const polygonCenterPoint = this.mapFeaturesService.getPolygonCenter(geoJSONFeatures);
            this.googleMaps.openGoogleMapsTabWithPin(polygonCenterPoint, zoom);
        }
    }

    public pointOnMarkerPositionOnGoogleMaps(): void {
        const storeValues = this.store.getValue();
        const zoom = this.mapStateWrapperService.zoom;
        const markerPosition = storeValues.markerPosition;
        this.googleMaps.openGoogleMapsTabWithPin(markerPosition, zoom);
    }

    public resetTitleData(): void {
        this.store.update({
            details: null,
            focusedFeatures: null,
            stateBeforeFocusing: null,
            isSelectionUnderPinBlocked: false,
        });
        this.urlParams.removeParams(['titleNumber', 'markerLat', 'markerLng']);
    }

    public getTitleDetails(folderId: string, titleNumber: string, { isCacheEnabled = false }: { isCacheEnabled?: boolean } = {}): Observable<ITitleInfo[]> {
        const cacheKey = `fid/${folderId}/title-number/${titleNumber}`;
        const observable = this.hmLandRegistryService.search(folderId, 'title-number', titleNumber, LandRegistrySearchSource.mapSearch)
            .pipe(
                catchError((error) => {
                    this.log.error(error);

                    return throwError(error);
                }),
            );

        return isCacheEnabled
            ? this.titleDetailsCacheService.findOrSet(cacheKey, observable.pipe(shareReplay(1)))
            : observable;
    }

    public setPreviousShownTitleId(titleNumber: string): void {
        this.store.update({ previousShowTitleId: titleNumber });
    }

    public initUrlParams(): void {
        const storedValues = this.store.getValue();
        const mapCenter = this.mapStateWrapperService.center;
        const params = {
            titleNumber: storedValues.details?.titleNumber,
            isFreeholdsOn: storedValues.isFreeholdsOn,
            isLeaseholdsOn: storedValues.isLeaseholdsOn,
            zoom: this.mapStateWrapperService.zoom,
            lat: mapCenter.lat,
            lng: mapCenter.lng,
        };

        this.urlParams.addParams({ ...params });
    }

    public activateSelectToolsMode(): void {
        this.mapPopupService.close();
        this.markerService.remove();
        this.markerService.block();
        this.mapPopupService.block();
        this.samStateService.enableSam();
        this.resetStateToSelectToolsMode();

        if (!this.isSelectToolsDisplayingPolygonsModeSub) {
            this.isSelectToolsDisplayingPolygonsModeSub = this.samStateQuery.selectIsDisplayingPolygonsModeEnabled()
                .subscribe((isDisplayingPolygonsMode) => {
                    if (isDisplayingPolygonsMode) {
                        this.mapPopupService.unblock();
                        this.markerService.unblock();
                    } else {
                        this.mapPopupService.close();
                        this.markerService.remove();
                        this.markerService.block();
                        this.mapPopupService.block();
                    }
                });
        }
    }

    public resetStateToSelectToolsMode(): void {
        this.store.update({
            featuresMap: null,
            loadedBounds: null,
            selectedFeatures: [],
            markerPosition: null,
            details: null,
            temporaryHighlightedTitleNumber: null,
            permanentlyHighlightedTitleNumber: null,
            sidePanelTitles: [],
            isSidebarVisible: false,
            isSelectionUnderPinBlocked: false,
            stateBeforeFocusing: null,
            previousShowTitleId: null,
        });
        this.mappingApi.cancelAll();
    }

    public deactivateSelectToolsMode(): void {
        this.samStateService.disableSam();
        this.mapPopupService.unblock();
        this.markerService.unblock();
        this.isSelectToolsDisplayingPolygonsModeSub?.unsubscribe();
        this.isSelectToolsDisplayingPolygonsModeSub = null;
    }

    public selectPolygonsExtractDataFromGeoJson(): void {
        const features = this.store.getValue().featuresMap ?? {};
        const titleDetails = this.mapFeaturesService.extractShortTitleInfoFromFeaturesMap(features);

        this.store.update({ details: null });
        this.chooseAllSelectedFeatures(titleDetails);
        this.setIsSidePanelVisible(true);
    }

    public setMarkerPosition(point?: LngLat): void {
        if (!point) {
            return this.store.update({ markerPosition: null });
        }

        this.store.update({ markerPosition: point });
        this.urlParams.addParams({ markerLat: point.lat, markerLng: point.lng });
    }

    public setIsFreeholdsOn(isFreeholdsOn: boolean): void {
        const isLeaseholdsOn = this.store.getValue().isLeaseholdsOn;
        this.updateMapFilter(isFreeholdsOn, isLeaseholdsOn);
    }

    public setIsLeaseholdsOn(isLeaseholdsOn: boolean): void {
        const isFreeholdsOn = this.store.getValue().isFreeholdsOn;
        this.updateMapFilter(isFreeholdsOn, isLeaseholdsOn);
    }

    public updateMapFilter(isFreeholdsOn: boolean, isLeaseholdsOn: boolean): void {
        this.store.update({ isLeaseholdsOn, isFreeholdsOn });
        this.urlParams.addParams({ isFreeholdsOn, isLeaseholdsOn });
    }

    public fetchMapGeoJson(bounds: MapBounds): void {
        const isZoomAcceptable = this.mapStateWrapperService.isZoomAboveMin;
        const featuresMap = this.store.getValue().featuresMap ?? {};
        const previousBounds = this.store.getValue().loadedBounds;
        const zoom = this.mapStateWrapperService.zoom;

        if (!isZoomAcceptable) {
            return;
        }

        const isEverythingExist = previousBounds && bounds && featuresMap && Object.keys(featuresMap).length;

        if (isEverythingExist) {
            const isPreviousBoundsIncludeNewOne = this.mapUtilsService.isPreviousBoundsIncludeNew(previousBounds, bounds);

            if (isPreviousBoundsIncludeNewOne) {
                return;
            }
        }

        this.store.setLoading(true);
        this.mappingApi.get(bounds, { zoom })
            .pipe(
                finalize(() => this.store.setLoading(false)),
            )
            .subscribe((geoJson) => {
                const selectedFeatures = this.store.getValue().selectedFeatures;
                const selectedAndNewFeatures = this.mapFeaturesService.joinUniqByPolyId(geoJson.features, selectedFeatures);
                const featuresMap = this.mapFeaturesService.convertToMap(selectedAndNewFeatures);

                this.store.update({ featuresMap: featuresMap, loadedBounds: bounds });
            });
    }

    public fetchGeoJsonForCircle(center: LngLat, radius: number): void {
        this.store.setLoading(true);
        this.mappingApi.getByCircle(center, radius, { isCacheEnabled: true })
            .pipe(
                finalize(() => this.store.setLoading(false)),
            )
            .subscribe((geoJsonWithSource) => {
                const geoJson = geoJsonWithSource.data;
                const featuresMap = this.mapFeaturesService.convertToMap(geoJson.features);
                this.store.update({
                    featuresMap: featuresMap,
                    isFreeholdsOn: true,
                    isLeaseholdsOn: true,
                });
            });
    }

    public fetchGeoJsonForPolygon(points: LngLat[]): void {
        this.store.setLoading(true);
        this.mappingApi.getByPolygon(points, { isCacheEnabled: true })
            .pipe(
                finalize(() => this.store.setLoading(false)),
            )
            .subscribe((geoJsonWithSource) => {
                const geoJson = geoJsonWithSource.data;
                const featuresMap = this.mapFeaturesService.convertToMap(geoJson.features);
                this.store.update({
                    featuresMap: featuresMap,
                    isFreeholdsOn: true,
                    isLeaseholdsOn: true,
                });
            });
    }

    public fetchTitleData(titleNumber: string): void {
        const previousDetails = this.store.getValue().details;
        const folderId = this.projectDetailsQuery.projectId;
        const isItPreviousSelectedTitleNumber = previousDetails && previousDetails.titleNumber === titleNumber;

        if (isItPreviousSelectedTitleNumber) {
            this.updateTitleDetails([previousDetails]);
        } else {
            this.store.update({ isSidePanelLoading: true });
            this.getTitleDetails(folderId, titleNumber, { isCacheEnabled: true })
                .subscribe((titles) => {
                    this.updateTitleDetails(titles);
                });
        }
    }

    public focusFeature(titleNumber: string): void {
        const featureMap = this.store.getValue().featuresMap ?? {};
        const features = featureMap[titleNumber];

        if (features) {
            const mappedFeature = Array.isArray(features) ? features : [features];
            const zoom = this.mapStateWrapperService.zoom;
            const center = this.mapStateWrapperService.center;

            this.store.update({
                focusedFeatures: mappedFeature,
                stateBeforeFocusing: { zoom, center },
                isSelectionUnderPinBlocked: true,
            });
        }
    }

    public restoreStateBeforeFocusing(): void {
        const state = this.store.getValue().stateBeforeFocusing;

        if (!state) {
            return;
        }

        this.mapNavigationService.jumpTo(state.center, state.zoom);
        this.store.update({
            focusedFeatures: null,
            stateBeforeFocusing: null,
            isSelectionUnderPinBlocked: false,
        });
    }

    public closeSidePanelAndResetHighlighting(): void {
        const isSidePanelLoading = this.store.getValue().isSidePanelLoading;

        if (isSidePanelLoading) {
            return;
        }

        this.setIsSidePanelVisible(false);
        this.resetHighlighting();
    }

    public setIsSidePanelVisible(isVisible: boolean): void {
        this.store.update({ isSidebarVisible: isVisible });

        const permanentlyHighlightedTitleNumber = this.store.getValue().permanentlyHighlightedTitleNumber;

        if (!isVisible && permanentlyHighlightedTitleNumber) {
            this.store.update({ permanentlyHighlightedTitleNumber: null });
        }
    }

    public highlightTitleTemporary(titleNumber: string): void {
        const debounceTimeMs = 100;

        if (this.highlightDebounceTimer) {
            clearTimeout(this.highlightDebounceTimer);
        }

        this.highlightDebounceTimer = setTimeout(() => {
            this.store.update({ temporaryHighlightedTitleNumber: titleNumber });
        }, debounceTimeMs);
    }

    public highlightFeaturePermanently(titleNumber: string): void {
        this.store.update({
            permanentlyHighlightedTitleNumber: titleNumber,
            searchedTitleNumber: null,
        });
    }

    public searchByAddress(placeId: string): void {
        this.setIsSidePanelVisible(false);
        this.resetTitleData();
        this.mappingApi.getDetailsByPlaceId(placeId)
            .subscribe((addressInfo) => {
                const location = addressInfo.geometry.location;

                this.store.update({
                    isSelectionUnderPinBlocked: false,
                    temporaryHighlightedTitleNumber: null,
                });
                this.mapStateWrapperService.applyMinZoom();
                this.updateMapFilter(false, false);
                this.mapNavigationService.jumpTo(location);
                this.setMarkerPosition(location);
                this.resetTitleData();
            });
    }

    public searchByTitleNumber(location: LngLat, titleNumber: string): void {
        if (!titleNumber) {
            return;
        }

        const currentTitleNumber = this.store.getValue().details?.titleNumber;
        const currentLocation = this.mapStateWrapperService.center;
        const markerLocation = this.store.getValue().markerPosition;
        const mapSearchZoomAfterSearch = this.profile.mapSearchMinZoomFeatureVisibility$.getValue();

        const isTitleNumberDifferent = currentTitleNumber !== titleNumber;
        if (isTitleNumberDifferent) {
            this.resetTitleData();
            this.store.update({ isSidePanelLoading: true });
        }

        const isLocationDifferent = this.mapUtilsService.isPointsDifferent(currentLocation, location);
        const isMarkerLocationDifferent = this.mapUtilsService.isPointsDifferent(currentLocation, markerLocation);
        if (isLocationDifferent || isMarkerLocationDifferent) {
            this.mapNavigationService.jumpTo(location);
            this.setMarkerPosition(location);
        }

        const isZoomLessThanMin = this.mapStateWrapperService.zoom < mapSearchZoomAfterSearch;
        if (isZoomLessThanMin) {
            this.mapStateWrapperService.setZoom(mapSearchZoomAfterSearch);
        }

        this.store.update({
            searchedTitleNumber: titleNumber,
            isSelectionUnderPinBlocked: false,
            temporaryHighlightedTitleNumber: null,
        });
        this.setIsSidePanelVisible(true);
        this.updateMapFilter(false, false);
    }

    public loadTitleFeatureLocationAndSearchByTitleNumber(titleNumber: string, markerPosition: { lat: number; lng: number }): void {
        const isMarkerPositionCorrect = !!markerPosition?.lng && !!markerPosition?.lat;

        if (!isMarkerPositionCorrect) {
            return;
        }

        const point = new LngLat(markerPosition?.lng, markerPosition?.lat);
        this.setMarkerPosition(point);
        this.mapStateWrapperService.setCenter(point);

        if (titleNumber) {
            this.fetchTitleData(titleNumber);
        }
    }

    public clearState(): void {
        this.store.update({
            details: null,
            focusedFeatures: null,
            stateBeforeFocusing: null,
            isSelectionUnderPinBlocked: false,
            selectedFeatures: [],
            isSidebarVisible: false,
            loadedBounds: null,
            searchedTitleNumber: null,
            markerPosition: null,
            featuresMap: null,
            previousShowTitleId: null,
            temporaryHighlightedTitleNumber: null,
            permanentlyHighlightedTitleNumber: null,
            sidePanelTitles: [],
            isSidePanelLoading: false,
            isFreeholdsOn: true,
            isLeaseholdsOn: true,
            isMapComponentInitialized: false,
        });
        this.urlParams.removeParams(this.urlMapParams);
    }

    public resetHighlighting(): void {
        this.store.update({
            temporaryHighlightedTitleNumber: null,
            permanentlyHighlightedTitleNumber: null,
        });
    }

    public resetTemporaryHighlighting(): void {
        this.store.update({
            temporaryHighlightedTitleNumber: null,
        });
    }

    public fetchSelectedTitles(titleNumber?: string, point?: LngLat): void {
        this.store.update({ isSidePanelLoading: true });

        this.mappingApi.selection({ isCacheEnabled: true, point, titleNumber })
            .subscribe((selectedTitles) => {
                this.chooseAllSelectedFeatures(selectedTitles);
                this.fetchOutsideFeatures();
                this.loadTitleDetailsIfOnlyATitleSelected(selectedTitles);

                const searchedTitleNumber = this.store.getValue().searchedTitleNumber;

                if (searchedTitleNumber) {
                    this.highlightFeaturePermanently(searchedTitleNumber);
                }
            });
    }

    public setMapComponentInitialized(): void {
        this.store.update({ isMapComponentInitialized: true });
    }

    public whichTitleNumberHighlight(features: GeoJSONFeature[]): string {
        const freeholdFeature = features.find((feature) => feature.properties.tenure?.toLowerCase() === 'freehold');
        const feature = freeholdFeature ? freeholdFeature : features[0];

        return feature?.properties.title_number || '';
    }

    private chooseAllSelectedFeatures(selectedTitles: ShortTitleInfo[]): void {
        const loadedFeatures = this.store.getValue().featuresMap ?? {};
        const selectedFeatures: GeoJSONFeature[] = [];

        selectedTitles.forEach((title) => {
            const titleNumber = title.titleNumber;
            const feature = loadedFeatures[titleNumber];

            if (feature) {
                if (Array.isArray(feature)) {
                    selectedFeatures.push(...feature);
                } else {
                    selectedFeatures.push(feature);
                }
            }
        });

        this.store.update({ sidePanelTitles: selectedTitles, selectedFeatures: selectedFeatures });
    }

    private loadTitleDetailsIfOnlyATitleSelected(selectedTitles: ShortTitleInfo[]): void {
        if (selectedTitles.length === 1) {
            const titleNumber = selectedTitles[0].titleNumber;
            this.fetchTitleData(titleNumber);
        } else {
            this.resetTitleData();
            this.store.update({ isSidePanelLoading: false });
        }
    }

    private fetchOutsideFeatures(): void {
        const isZoomAcceptable = this.mapStateWrapperService.isZoomAboveMin;
        const isBoundsSet = !!this.store.getValue().loadedBounds;

        if (!isZoomAcceptable || !isBoundsSet) {
            return;
        }

        const zoom = this.mapStateWrapperService.zoom;
        const features = this.store.getValue().featuresMap ?? {};
        const titlesNumbersFromSidePanel = this.store.getValue().sidePanelTitles
            .map((el) => el.titleNumber);
        const titleNumbersWithoutPolygons = titlesNumbersFromSidePanel.filter((titleNumber) => !features[titleNumber]);
        const isTitleTitleNumbersWithoutPolygonsExist = !!titleNumbersWithoutPolygons.length;

        if (!isTitleTitleNumbersWithoutPolygonsExist) {
            return;
        }

        this.mappingApi.get(null, { zoom, includeTitles: titleNumbersWithoutPolygons })
            .pipe(
                finalize(() => this.store.setLoading(false)),
            )
            .subscribe((geoJson) => {
                const features = this.store.getValue().featuresMap ?? {};
                const newFeatures = geoJson.features;
                const resultFeatures = { ...features };

                newFeatures.forEach((newFeature) => {
                    const newFeatureTitleNumber = newFeature.properties.title_number;
                    const newFeaturePolyId = newFeature.properties.poly_id;
                    const existedFeatures = resultFeatures[newFeatureTitleNumber];

                    if (!existedFeatures) {
                        resultFeatures[newFeatureTitleNumber] = newFeature;

                        return;
                    }

                    if (Array.isArray(existedFeatures)) {
                        const isFeatureExists = existedFeatures.some((el) => el.properties.poly_id === newFeaturePolyId);
                        if (!isFeatureExists) {
                            existedFeatures.push(newFeature);
                        }
                    } else {
                        const isFeatureExists = existedFeatures.properties.poly_id === newFeaturePolyId;
                        if (!isFeatureExists) {
                            resultFeatures[existedFeatures.properties.title_number] = [existedFeatures, newFeature];
                        }
                    }
                });

                this.store.update({ featuresMap: resultFeatures });
            });
    }

    private updateTitleDetails(titles: ITitleInfo[]): void {
        if (!titles) {
            this.store.update({ isSidePanelLoading: false });
            return;
        }

        const firstTitle = titles[0];
        this.store.update({ details: firstTitle, isSidePanelLoading: false });
        this.urlParams.addParams({ 'titleNumber': firstTitle.titleNumber });
    }

    private subscribeOnMarkerContextMenuEvents(): void {
        this.markerService.contextMenuEvent.subscribe((action) => {
            if (action === MarkerMenuAction.viewOnGoogleMaps) {
                this.pointOnMarkerPositionOnGoogleMaps();
            }
        });
    }
}
